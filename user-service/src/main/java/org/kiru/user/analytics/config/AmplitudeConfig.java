package org.kiru.user.analytics.config;

import com.amplitude.Amplitude;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

@Slf4j
@Configuration
public class AmplitudeConfig {

    @Value("${amplitude.api-key:}")
    private String apiKey;

    @Value("${amplitude.enabled:true}")
    private boolean enabled;

    @Bean
    public Amplitude amplitude() {
        if (!enabled) {
            log.info("Amplitude is disabled");
            return null;
        }

        if (apiKey == null || apiKey.trim().isEmpty()) {
            log.warn("Amplitude API key is not configured. Analytics will be disabled.");
            return null;
        }

        try {
            Amplitude amplitude = Amplitude.getInstance();
            amplitude.init(apiKey);
            log.info("Amplitude initialized successfully with API key: {}***", 
                    apiKey.substring(0, Math.min(8, apiKey.length())));
            return amplitude;
        } catch (Exception e) {
            log.error("Failed to initialize Amplitude", e);
            return null;
        }
    }
}
